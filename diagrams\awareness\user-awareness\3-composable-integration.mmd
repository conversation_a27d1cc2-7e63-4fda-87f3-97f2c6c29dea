graph TD
    A[User Awareness Components] --> B[useAwarenessActions]
    A --> C[useAwarenessPageState]
    
    D[AwarenessEvaluationDashboard.vue] --> B
    E[CourseList.vue] --> B
    F[CoursePreview.vue] --> B
    G[LessonPreview.vue] --> B
    H[QuizTaking.vue] --> B
    
    B --> B1[State Management]
    B --> B2[API Operations]
    B --> B3[Business Logic]
    B --> B4[Navigation]
    
    B1 --> B1a[loading]
    B1 --> B1b[courses]
    B1 --> B1c[currentCourse]
    B1 --> B1d[currentLesson]
    B1 --> B1e[questions]
    B1 --> B1f[quizResult]
    B1 --> B1g[currentQuestionIndex]
    
    B2 --> B2a[getAwarenessEvaluationsWithProgress]
    B2 --> B2b[getAvailableCourses]
    B2 --> B2c[getSingleCourse]
    B2 --> B2d[getSingleLesson]
    B2 --> B2e[markAsComplete]
    B2 --> B2f[submitQuiz]
    
    B3 --> B3a[getOverallProgress]
    B3 --> B3b[getStatusIcon]
    B3 --> B3c[getProgressClass]
    B3 --> B3d[getScoreClass]
    B3 --> B3e[formatDate]
    
    B4 --> B4a[startCourse]
    B4 --> B4b[prevQuestion]
    B4 --> B4c[nextQuestion]
    B4 --> B4d[goToQuestion]
    
    C --> C1[Route State]
    C1 --> C1a[courseId]
    C1 --> C1b[topicId]
    C1 --> C1c[lessonId]
    C1 --> C1d[activeTopicId]
    C1 --> C1e[activeLessonId]
    
    style A fill:#e8f5e8
    style B fill:#bbdefb
    style C fill:#f3e5f5
