graph TD
    A[User Awareness Pages] --> B[Dashboard]
    A --> C[CourseList]
    A --> D[CoursePreview]
    A --> E[LessonPreview]
    
    B --> B1[AwarenessEvaluationDashboard.vue]
    B1 --> B2[UserCourseCard.vue]
    B1 --> B3[CourseProgressBar.vue]
    
    C --> C1[CourseList.vue]
    C1 --> C2[CourseListCard.vue]
    C1 --> C3[CourseListStats.vue]
    C1 --> C4[CourseListBenefits.vue]
    
    D --> D1[CoursePreview.vue]
    D1 --> D2[UserCourseBreadcrumb.vue]
    D1 --> D3[UserCourseSidebar.vue]
    D1 --> D4[UserCourseOverviewStats.vue]
    D1 --> D5[UserCourseActionSection.vue]
    D1 --> D6[UserCourseProgressCircle.vue]
    
    E --> E1[LessonPreview.vue]
    E1 --> E2[LessonHeader.vue]
    E1 --> E3[LessonContent.vue]
    E1 --> E4[QuizTaking.vue]
    E1 --> E5[QuizResults.vue]
    
    F[General Components] --> F1[UserPageHeader.vue]
    F --> F2[UserLoadingState.vue]
    F --> F3[EmptyState.vue]
    F --> F4[FeatureStats.vue]
    F --> F5[NewFeatureCard.vue]
    
    B1 --> F1
    B1 --> F2
    B1 --> F3
    B1 --> F4
    B1 --> F5
    
    C1 --> F1
    C1 --> F2
    C1 --> F3
    
    D1 --> F2
    
    style A fill:#e3f2fd
    style F fill:#fff3e0
    style F1 fill:#ffcc02
    style F2 fill:#ffcc02
    style F3 fill:#ffcc02
    style F4 fill:#ffcc02
    style F5 fill:#ffcc02
